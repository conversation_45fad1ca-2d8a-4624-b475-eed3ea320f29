from flask import render_template, flash, redirect, url_for, request, jsonify
from app import app, db
from app.forms import TradeForm, DailySummaryForm, ImportTradesForm
from app.models.models import Trade, DailySummary
from datetime import datetime, time
import requests
import json
import pandas as pd
import os
from werkzeug.utils import secure_filename

@app.route('/')
@app.route('/index')
def index():
    return render_template('index.html', title='股市交易记录系统')

@app.route('/add_trade', methods=['GET', 'POST'])
def add_trade():
    form = TradeForm()
    if form.validate_on_submit():
        total_amount = form.price.data * form.quantity.data
        trade = Trade(
            date=form.date.data,
            time=form.time.data,
            stock_name=form.stock_name.data,
            stock_code=form.stock_code.data,
            trade_type=form.trade_type.data,
            price=form.price.data,
            quantity=form.quantity.data,
            total_amount=total_amount,
            notes=form.notes.data
        )
        db.session.add(trade)
        db.session.commit()
        flash('交易记录已添加！')
        return redirect(url_for('view_trades'))
    return render_template('add_trade.html', title='添加交易记录', form=form)

@app.route('/view_trades')
def view_trades():
    page = request.args.get('page', 1, type=int)
    trades = Trade.query.order_by(Trade.date.desc(), Trade.time.desc()).paginate(
        page=page, per_page=10)
    return render_template('view_trades.html', title='查看交易记录', trades=trades)

@app.route('/add_summary', methods=['GET', 'POST'])
def add_summary():
    form = DailySummaryForm()
    if form.validate_on_submit():
        # 检查是否已存在该日期的总结
        existing_summary = DailySummary.query.filter_by(date=form.date.data).first()
        if existing_summary:
            existing_summary.profit_loss = form.profit_loss.data
            existing_summary.summary = form.summary.data
            db.session.commit()
            flash('每日总结已更新！')
        else:
            summary = DailySummary(
                date=form.date.data,
                profit_loss=form.profit_loss.data,
                summary=form.summary.data
            )
            db.session.add(summary)
            db.session.commit()
            flash('每日总结已添加！')
        return redirect(url_for('view_summaries'))
    return render_template('add_summary.html', title='添加每日总结', form=form)

@app.route('/view_summaries')
def view_summaries():
    page = request.args.get('page', 1, type=int)
    summaries = DailySummary.query.order_by(DailySummary.date.desc()).paginate(
        page=page, per_page=10)
    return render_template('view_summaries.html', title='查看每日总结', summaries=summaries)

@app.route('/trade/<int:id>')
def trade_detail(id):
    trade = Trade.query.get_or_404(id)
    return render_template('trade_detail.html', title='交易详情', trade=trade)

@app.route('/edit_trade/<int:id>', methods=['GET', 'POST'])
def edit_trade(id):
    trade = Trade.query.get_or_404(id)
    form = TradeForm(obj=trade)

    if form.validate_on_submit():
        # 更新交易记录
        trade.date = form.date.data
        trade.time = form.time.data
        trade.stock_name = form.stock_name.data
        trade.stock_code = form.stock_code.data
        trade.trade_type = form.trade_type.data
        trade.price = form.price.data
        trade.quantity = form.quantity.data
        trade.total_amount = form.price.data * form.quantity.data
        trade.notes = form.notes.data

        db.session.commit()
        flash('交易记录已更新！')
        return redirect(url_for('trade_detail', id=trade.id))

    return render_template('edit_trade.html', title='编辑交易记录', form=form, trade=trade)

@app.route('/delete_trade/<int:id>', methods=['POST'])
def delete_trade(id):
    trade = Trade.query.get_or_404(id)

    try:
        db.session.delete(trade)
        db.session.commit()
        flash('交易记录已删除！')
        return jsonify({'success': True, 'message': '交易记录已删除'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'删除失败: {str(e)}'})

@app.route('/summary/<int:id>')
def summary_detail(id):
    summary = DailySummary.query.get_or_404(id)
    # 获取该日期的所有交易
    trades = Trade.query.filter_by(date=summary.date).all()
    return render_template('summary_detail.html', title='每日总结详情', summary=summary, trades=trades)

@app.route('/edit_summary/<int:id>', methods=['GET', 'POST'])
def edit_summary(id):
    summary = DailySummary.query.get_or_404(id)
    form = DailySummaryForm(obj=summary)

    if form.validate_on_submit():
        # 更新每日总结
        summary.date = form.date.data
        summary.profit_loss = form.profit_loss.data
        summary.summary = form.summary.data

        db.session.commit()
        flash('每日总结已更新！')
        return redirect(url_for('summary_detail', id=summary.id))

    return render_template('edit_summary.html', title='编辑每日总结', form=form, summary=summary)

@app.route('/delete_summary/<int:id>', methods=['POST'])
def delete_summary(id):
    summary = DailySummary.query.get_or_404(id)

    try:
        db.session.delete(summary)
        db.session.commit()
        flash('每日总结已删除！')
        return jsonify({'success': True, 'message': '每日总结已删除'})
    except Exception as e:
        db.session.rollback()
        return jsonify({'success': False, 'message': f'删除失败: {str(e)}'})

@app.route('/test_stock')
def test_stock():
    return render_template('test_stock.html', title='测试股票代码查询')

@app.route('/import_trades', methods=['GET', 'POST'])
def import_trades():
    form = ImportTradesForm()

    if form.validate_on_submit():
        file = form.file.data
        filename = secure_filename(file.filename)

        # 保存上传的文件
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(filepath)

        try:
            # 解析Excel文件
            success_count, error_count, errors = parse_excel_file(filepath)

            # 删除临时文件
            os.remove(filepath)

            if success_count > 0:
                flash(f'成功导入 {success_count} 条交易记录！')
            if error_count > 0:
                flash(f'有 {error_count} 条记录导入失败，请检查数据格式。')
                for error in errors[:5]:  # 只显示前5个错误
                    flash(f'错误：{error}', 'error')

            return redirect(url_for('view_trades'))

        except Exception as e:
            # 删除临时文件
            if os.path.exists(filepath):
                os.remove(filepath)
            flash(f'文件解析失败：{str(e)}', 'error')

    return render_template('import_trades.html', title='导入交易记录', form=form)

@app.route('/get_stock_name')
def get_stock_name():
    stock_code = request.args.get('code', '')
    if not stock_code:
        return jsonify({'success': False, 'message': '股票代码不能为空'})

    try:
        # 使用腾讯股票API获取股票信息
        url = f'http://qt.gtimg.cn/q={stock_code}'
        response = requests.get(url, timeout=5)
        response.encoding = 'gbk'  # 设置正确的编码

        # 解析返回的数据
        data = response.text
        app.logger.info(f"API返回数据: {data}")

        # 检查是否包含股票信息
        if 'v_' + stock_code not in data:
            app.logger.warning(f"未找到股票信息: {stock_code}")
            return jsonify({'success': False, 'message': '未找到股票信息'})

        # 提取股票名称 - 使用更准确的方法
        # 格式通常是: v_sz000001="51~平安银行~000001~11.46~...
        # 我们需要提取第二个波浪线之间的内容

        # 首先找到第一个引号后的内容
        quote_pos = data.find('"')
        if quote_pos != -1:
            # 从引号后开始分割
            content_after_quote = data[quote_pos+1:]
            parts = content_after_quote.split('~')

            # 股票名称通常是第二个部分
            if len(parts) >= 2:
                stock_name = parts[1]
                app.logger.info(f"提取的股票名称: {stock_name}")
                return jsonify({'success': True, 'name': stock_name, 'code': stock_code})

        # 如果上面的方法失败，尝试原来的方法
        start_index = data.find('v_' + stock_code + '="')
        if start_index == -1:
            app.logger.warning(f"无法找到股票代码开始位置: {stock_code}")
            return jsonify({'success': False, 'message': '解析股票信息失败'})

        start_index += len('v_' + stock_code + '="')
        end_index = data.find('~', start_index)
        if end_index == -1:
            app.logger.warning(f"无法找到股票信息分隔符: {stock_code}")
            return jsonify({'success': False, 'message': '解析股票信息失败'})

        # 提取并分割数据
        data_segment = data[start_index:end_index]
        app.logger.info(f"提取的数据段: {data_segment}")

        stock_info = data[start_index:].split('~')
        if len(stock_info) < 2:
            app.logger.warning(f"股票信息格式不正确: {stock_info}")
            return jsonify({'success': False, 'message': '解析股票名称失败'})

        stock_name = stock_info[1]
        app.logger.info(f"提取的股票名称: {stock_name}")
        return jsonify({'success': True, 'name': stock_name, 'code': stock_code})

    except Exception as e:
        app.logger.error(f"获取股票名称出错: {str(e)}")
        return jsonify({'success': False, 'message': f'获取股票信息失败: {str(e)}'})

def parse_excel_file(filepath):
    """
    解析Excel文件并导入交易记录
    返回: (成功数量, 错误数量, 错误列表)
    """
    success_count = 0
    error_count = 0
    errors = []

    try:
        # 读取Excel文件
        df = pd.read_excel(filepath)

        # 检查必要的列是否存在
        required_columns = ['成交时间', '证券代码', '证券名称', '买卖方向', '成交数量', '成交价格']
        missing_columns = [col for col in required_columns if col not in df.columns]

        if missing_columns:
            raise ValueError(f"Excel文件缺少必要的列: {', '.join(missing_columns)}")

        # 遍历每一行数据
        for index, row in df.iterrows():
            try:
                # 解析成交时间
                trade_datetime_str = str(row['成交时间']).strip()
                if pd.isna(row['成交时间']) or trade_datetime_str == 'nan':
                    errors.append(f"第{index+2}行：成交时间为空")
                    error_count += 1
                    continue

                # 尝试解析时间格式
                try:
                    if ':' in trade_datetime_str:
                        # 格式如 "15:00:00.002136"
                        time_part = trade_datetime_str.split('.')[0]  # 去掉毫秒
                        trade_time = datetime.strptime(time_part, '%H:%M:%S').time()
                        trade_date = datetime.now().date()  # 使用当前日期
                    else:
                        # 可能是其他格式，尝试解析
                        trade_datetime = pd.to_datetime(trade_datetime_str)
                        trade_date = trade_datetime.date()
                        trade_time = trade_datetime.time()
                except:
                    errors.append(f"第{index+2}行：时间格式无法解析 - {trade_datetime_str}")
                    error_count += 1
                    continue

                # 解析股票代码和名称
                stock_code = str(row['证券代码']).strip()
                stock_name = str(row['证券名称']).strip()

                if pd.isna(row['证券代码']) or stock_code == 'nan':
                    errors.append(f"第{index+2}行：证券代码为空")
                    error_count += 1
                    continue

                if pd.isna(row['证券名称']) or stock_name == 'nan':
                    errors.append(f"第{index+2}行：证券名称为空")
                    error_count += 1
                    continue

                # 解析买卖方向
                trade_direction = str(row['买卖方向']).strip()
                if '买' in trade_direction or '买入' in trade_direction:
                    trade_type = '买入'
                elif '卖' in trade_direction or '卖出' in trade_direction:
                    trade_type = '卖出'
                else:
                    errors.append(f"第{index+2}行：无法识别买卖方向 - {trade_direction}")
                    error_count += 1
                    continue

                # 解析数量和价格
                try:
                    quantity = int(float(row['成交数量']))
                    price = float(row['成交价格'])
                except (ValueError, TypeError):
                    errors.append(f"第{index+2}行：数量或价格格式错误")
                    error_count += 1
                    continue

                # 计算总金额
                total_amount = price * quantity

                # 创建交易记录
                trade = Trade(
                    date=trade_date,
                    time=trade_time,
                    stock_name=stock_name,
                    stock_code=stock_code,
                    trade_type=trade_type,
                    price=price,
                    quantity=quantity,
                    total_amount=total_amount,
                    notes=f"从Excel导入 - 行{index+2}"
                )

                db.session.add(trade)
                success_count += 1

            except Exception as e:
                errors.append(f"第{index+2}行：{str(e)}")
                error_count += 1
                continue

        # 提交所有成功的记录
        if success_count > 0:
            db.session.commit()

    except Exception as e:
        db.session.rollback()
        raise e

    return success_count, error_count, errors
