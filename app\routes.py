from flask import render_template, flash, redirect, url_for, request
from app import app, db
from app.forms import TradeForm, DailySummaryForm
from app.models.models import Trade, DailySummary
from datetime import datetime

@app.route('/')
@app.route('/index')
def index():
    return render_template('index.html', title='股市交易记录系统')

@app.route('/add_trade', methods=['GET', 'POST'])
def add_trade():
    form = TradeForm()
    if form.validate_on_submit():
        total_amount = form.price.data * form.quantity.data
        trade = Trade(
            date=form.date.data,
            time=form.time.data,
            stock_name=form.stock_name.data,
            stock_code=form.stock_code.data,
            trade_type=form.trade_type.data,
            price=form.price.data,
            quantity=form.quantity.data,
            total_amount=total_amount,
            notes=form.notes.data
        )
        db.session.add(trade)
        db.session.commit()
        flash('交易记录已添加！')
        return redirect(url_for('view_trades'))
    return render_template('add_trade.html', title='添加交易记录', form=form)

@app.route('/view_trades')
def view_trades():
    page = request.args.get('page', 1, type=int)
    trades = Trade.query.order_by(Trade.date.desc(), Trade.time.desc()).paginate(
        page=page, per_page=10)
    return render_template('view_trades.html', title='查看交易记录', trades=trades)

@app.route('/add_summary', methods=['GET', 'POST'])
def add_summary():
    form = DailySummaryForm()
    if form.validate_on_submit():
        # 检查是否已存在该日期的总结
        existing_summary = DailySummary.query.filter_by(date=form.date.data).first()
        if existing_summary:
            existing_summary.profit_loss = form.profit_loss.data
            existing_summary.summary = form.summary.data
            db.session.commit()
            flash('每日总结已更新！')
        else:
            summary = DailySummary(
                date=form.date.data,
                profit_loss=form.profit_loss.data,
                summary=form.summary.data
            )
            db.session.add(summary)
            db.session.commit()
            flash('每日总结已添加！')
        return redirect(url_for('view_summaries'))
    return render_template('add_summary.html', title='添加每日总结', form=form)

@app.route('/view_summaries')
def view_summaries():
    page = request.args.get('page', 1, type=int)
    summaries = DailySummary.query.order_by(DailySummary.date.desc()).paginate(
        page=page, per_page=10)
    return render_template('view_summaries.html', title='查看每日总结', summaries=summaries)

@app.route('/trade/<int:id>')
def trade_detail(id):
    trade = Trade.query.get_or_404(id)
    return render_template('trade_detail.html', title='交易详情', trade=trade)

@app.route('/summary/<int:id>')
def summary_detail(id):
    summary = DailySummary.query.get_or_404(id)
    # 获取该日期的所有交易
    trades = Trade.query.filter_by(date=summary.date).all()
    return render_template('summary_detail.html', title='每日总结详情', summary=summary, trades=trades)
