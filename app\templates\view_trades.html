{% extends "base.html" %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h2 class="card-title">交易记录列表</h2>
            </div>
            <div class="card-body">
                {% if trades.items %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>日期</th>
                                <th>时间</th>
                                <th>股票名称</th>
                                <th>股票代码</th>
                                <th>交易类型</th>
                                <th>价格</th>
                                <th>数量</th>
                                <th>总金额</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for trade in trades.items %}
                            <tr>
                                <td>{{ trade.date.strftime('%Y-%m-%d') }}</td>
                                <td>{{ trade.time.strftime('%H:%M') }}</td>
                                <td>{{ trade.stock_name }}</td>
                                <td>{{ trade.stock_code }}</td>
                                <td>
                                    {% if trade.trade_type == '买入' %}
                                    <span class="badge bg-success">买入</span>
                                    {% else %}
                                    <span class="badge bg-danger">卖出</span>
                                    {% endif %}
                                </td>
                                <td>{{ trade.price }}</td>
                                <td>{{ trade.quantity }}</td>
                                <td>{{ trade.total_amount }}</td>
                                <td>
                                    <a href="{{ url_for('trade_detail', id=trade.id) }}" class="btn btn-sm btn-info">详情</a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center">
                        {% if trades.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('view_trades', page=trades.prev_num) }}">上一页</a>
                        </li>
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">上一页</span>
                        </li>
                        {% endif %}
                        
                        {% for page_num in trades.iter_pages(left_edge=1, right_edge=1, left_current=1, right_current=2) %}
                            {% if page_num %}
                                {% if page_num == trades.page %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% else %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('view_trades', page=page_num) }}">{{ page_num }}</a>
                                </li>
                                {% endif %}
                            {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if trades.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('view_trades', page=trades.next_num) }}">下一页</a>
                        </li>
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">下一页</span>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% else %}
                <div class="alert alert-info">
                    暂无交易记录。<a href="{{ url_for('add_trade') }}">添加一条交易记录</a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
