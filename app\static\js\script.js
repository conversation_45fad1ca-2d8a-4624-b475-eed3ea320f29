// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 设置日期输入框默认值为今天
    const dateInputs = document.querySelectorAll('input[type="date"]');
    const today = new Date().toISOString().split('T')[0];
    
    dateInputs.forEach(input => {
        if (!input.value) {
            input.value = today;
        }
    });
    
    // 设置时间输入框默认值为当前时间
    const timeInputs = document.querySelectorAll('input[type="time"]');
    const now = new Date();
    const currentTime = String(now.getHours()).padStart(2, '0') + ':' + String(now.getMinutes()).padStart(2, '0');
    
    timeInputs.forEach(input => {
        if (!input.value) {
            input.value = currentTime;
        }
    });
    
    // 计算总金额
    const priceInput = document.querySelector('input[name="price"]');
    const quantityInput = document.querySelector('input[name="quantity"]');
    
    if (priceInput && quantityInput) {
        const calculateTotal = function() {
            const price = parseFloat(priceInput.value) || 0;
            const quantity = parseInt(quantityInput.value) || 0;
            const total = price * quantity;
            
            // 如果有总金额显示元素，则更新它
            const totalElement = document.getElementById('total-amount');
            if (totalElement) {
                totalElement.textContent = total.toFixed(2);
            }
        };
        
        priceInput.addEventListener('input', calculateTotal);
        quantityInput.addEventListener('input', calculateTotal);
    }
});
