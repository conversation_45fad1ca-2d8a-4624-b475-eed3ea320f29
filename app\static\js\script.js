// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 设置日期输入框默认值为今天
    const dateInputs = document.querySelectorAll('input[type="date"]');
    const today = new Date().toISOString().split('T')[0];

    dateInputs.forEach(input => {
        if (!input.value) {
            input.value = today;
        }
    });

    // 设置时间输入框默认值为当前时间
    const timeInputs = document.querySelectorAll('input[type="time"]');
    const now = new Date();
    const currentTime = String(now.getHours()).padStart(2, '0') + ':' + String(now.getMinutes()).padStart(2, '0');

    timeInputs.forEach(input => {
        if (!input.value) {
            input.value = currentTime;
        }
    });

    // 计算总金额
    const priceInput = document.querySelector('input[name="price"]');
    const quantityInput = document.querySelector('input[name="quantity"]');

    if (priceInput && quantityInput) {
        const calculateTotal = function() {
            const price = parseFloat(priceInput.value) || 0;
            const quantity = parseInt(quantityInput.value) || 0;
            const total = price * quantity;

            // 如果有总金额显示元素，则更新它
            const totalElement = document.getElementById('total-amount');
            if (totalElement) {
                totalElement.textContent = total.toFixed(2);
            }
        };

        priceInput.addEventListener('input', calculateTotal);
        quantityInput.addEventListener('input', calculateTotal);
    }

    // 自动获取股票名称
    const stockCodeInput = document.querySelector('input[name="stock_code"]');
    const stockNameInput = document.querySelector('input[name="stock_name"]');

    if (stockCodeInput && stockNameInput) {
        stockCodeInput.addEventListener('blur', function() {
            const stockCode = stockCodeInput.value.trim();
            if (stockCode) {
                // 显示加载提示
                stockNameInput.value = "正在获取...";
                stockNameInput.disabled = true;

                // 处理股票代码格式
                let formattedCode = stockCode;
                // 如果只输入数字，根据规则添加前缀
                if (/^\d+$/.test(stockCode)) {
                    if (stockCode.startsWith('6')) {
                        formattedCode = 'sh' + stockCode;
                    } else if (stockCode.startsWith('0') || stockCode.startsWith('3')) {
                        formattedCode = 'sz' + stockCode;
                    } else if (stockCode.startsWith('4') || stockCode.startsWith('8')) {
                        formattedCode = 'bj' + stockCode;
                    } else if (stockCode.startsWith('5') || stockCode.startsWith('1')) {
                        formattedCode = 'sh' + stockCode;
                    }
                }

                // 调用后端API获取股票名称
                fetch('/get_stock_name?code=' + encodeURIComponent(formattedCode))
                    .then(response => response.json())
                    .then(data => {
                        if (data.success && data.name) {
                            stockNameInput.value = data.name;
                        } else {
                            stockNameInput.value = "";
                            alert('未找到股票信息，请检查股票代码是否正确');
                        }
                        stockNameInput.disabled = false;
                    })
                    .catch(error => {
                        console.error('获取股票名称失败:', error);
                        stockNameInput.value = "";
                        stockNameInput.disabled = false;
                        alert('获取股票名称失败，请手动输入');
                    });
            }
        });
    }
});
