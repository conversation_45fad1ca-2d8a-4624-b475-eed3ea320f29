// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', function() {
    // 设置日期输入框默认值为今天
    const dateInputs = document.querySelectorAll('input[type="date"]');
    const today = new Date().toISOString().split('T')[0];

    dateInputs.forEach(input => {
        if (!input.value) {
            input.value = today;
        }
    });

    // 设置时间输入框默认值为当前时间
    const timeInputs = document.querySelectorAll('input[type="time"]');
    const now = new Date();
    const currentTime = String(now.getHours()).padStart(2, '0') + ':' + String(now.getMinutes()).padStart(2, '0');

    timeInputs.forEach(input => {
        if (!input.value) {
            input.value = currentTime;
        }
    });

    // 计算总金额
    const priceInput = document.querySelector('input[name="price"]');
    const quantityInput = document.querySelector('input[name="quantity"]');

    if (priceInput && quantityInput) {
        const calculateTotal = function() {
            const price = parseFloat(priceInput.value) || 0;
            const quantity = parseInt(quantityInput.value) || 0;
            const total = price * quantity;

            // 如果有总金额显示元素，则更新它
            const totalElement = document.getElementById('total-amount');
            if (totalElement) {
                totalElement.textContent = total.toFixed(2);
            }
        };

        priceInput.addEventListener('input', calculateTotal);
        quantityInput.addEventListener('input', calculateTotal);
    }

    // 自动获取股票名称
    const stockCodeInput = document.querySelector('input[name="stock_code"]');
    const stockNameInput = document.querySelector('input[name="stock_name"]');
    const testStockCodeBtn = document.getElementById('test-stock-code');

    // 定义获取股票名称的函数
    const getStockName = function() {
        const stockCode = stockCodeInput.value.trim();
        if (stockCode) {
            // 显示加载提示
            stockNameInput.value = "正在获取...";
            stockNameInput.disabled = true;

            // 处理股票代码格式
            let formattedCode = stockCode;

            // 如果已经包含前缀，直接使用
            if (stockCode.startsWith('sh') || stockCode.startsWith('sz') || stockCode.startsWith('bj')) {
                formattedCode = stockCode;
            }
            // 如果只输入数字，根据规则添加前缀
            else if (/^\d+$/.test(stockCode)) {
                // 确保股票代码至少有6位数字
                let paddedCode = stockCode.padStart(6, '0');

                if (paddedCode.startsWith('6')) {
                    formattedCode = 'sh' + paddedCode;
                } else if (paddedCode.startsWith('0') || paddedCode.startsWith('3')) {
                    formattedCode = 'sz' + paddedCode;
                } else if (paddedCode.startsWith('4') || paddedCode.startsWith('8')) {
                    formattedCode = 'bj' + paddedCode;
                } else if (paddedCode.startsWith('5') || paddedCode.startsWith('1')) {
                    formattedCode = 'sh' + paddedCode;
                }
            }

            console.log("原始代码:", stockCode, "格式化后:", formattedCode);

            // 调用后端API获取股票名称
            fetch('/get_stock_name?code=' + encodeURIComponent(formattedCode))
                .then(response => {
                    console.log("API响应状态:", response.status);
                    return response.json();
                })
                .then(data => {
                    console.log("API返回数据:", data);
                    if (data.success && data.name) {
                        stockNameInput.value = data.name;
                    } else {
                        stockNameInput.value = "";
                        alert('未找到股票信息，请检查股票代码是否正确');
                    }
                    stockNameInput.disabled = false;
                })
                .catch(error => {
                    console.error('获取股票名称失败:', error);
                    stockNameInput.value = "";
                    stockNameInput.disabled = false;
                    alert('获取股票名称失败，请手动输入');
                });
        }
    };

    if (stockCodeInput && stockNameInput) {
        // 当股票代码输入框失去焦点时获取股票名称
        stockCodeInput.addEventListener('blur', getStockName);

        // 测试按钮点击事件
        if (testStockCodeBtn) {
            testStockCodeBtn.addEventListener('click', getStockName);
        }
    }
});

// 删除交易记录的函数
function deleteTradeRecord(tradeId) {
    if (confirm('确定要删除这条交易记录吗？此操作不可撤销！')) {
        fetch('/delete_trade/' + tradeId, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('交易记录已删除！');
                // 如果在列表页面，刷新页面
                if (window.location.pathname === '/view_trades') {
                    window.location.reload();
                } else {
                    // 如果在详情页面，跳转到列表页面
                    window.location.href = '/view_trades';
                }
            } else {
                alert('删除失败：' + data.message);
            }
        })
        .catch(error => {
            console.error('删除失败:', error);
            alert('删除失败，请检查网络连接');
        });
    }
}

// 删除每日总结的函数
function deleteSummaryRecord(summaryId) {
    if (confirm('确定要删除这条每日总结吗？此操作不可撤销！')) {
        fetch('/delete_summary/' + summaryId, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('每日总结已删除！');
                // 如果在列表页面，刷新页面
                if (window.location.pathname === '/view_summaries') {
                    window.location.reload();
                } else {
                    // 如果在详情页面，跳转到列表页面
                    window.location.href = '/view_summaries';
                }
            } else {
                alert('删除失败：' + data.message);
            }
        })
        .catch(error => {
            console.error('删除失败:', error);
            alert('删除失败，请检查网络连接');
        });
    }
}
