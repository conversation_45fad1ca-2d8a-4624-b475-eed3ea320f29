import subprocess
import webbrowser
import time
import sys
import os

def main():
    print("正在启动股票交易记录系统...")
    
    # 启动Flask应用
    flask_process = subprocess.Popen([sys.executable, "run.py"], 
                                    stdout=subprocess.PIPE,
                                    stderr=subprocess.PIPE,
                                    creationflags=subprocess.CREATE_NO_WINDOW)
    
    # 等待服务器启动
    print("等待服务器启动...")
    time.sleep(2)
    
    # 打开浏览器
    print("正在打开浏览器...")
    webbrowser.open("http://127.0.0.1:5000")
    
    print("\n系统已启动！请在浏览器中使用。")
    print("按Ctrl+C停止系统运行...\n")
    
    try:
        # 保持脚本运行，直到用户按下Ctrl+C
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n正在停止系统...")
        flask_process.terminate()
        flask_process.wait(timeout=5)
        print("系统已停止。")

if __name__ == "__main__":
    main()
