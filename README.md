# 股市交易记录系统

这是一个用于记录股市交易每日实盘记录的Web应用程序。

## 功能特点

- 记录交易日期、时间
- 记录买入/卖出股票的具体名称和代码
- 记录买入/卖出股票的价格和数量
- 自动根据股票代码获取股票名称
- 提供每日交易总结功能
- 查看历史交易记录和总结
- 编辑和删除交易记录
- 编辑和删除每日总结

## 技术栈

- Python 3.8+
- Flask Web框架
- SQLite数据库
- Bootstrap 5 前端框架

## 安装步骤

1. 克隆或下载本项目到本地

2. 创建并激活虚拟环境（可选但推荐）

```
python -m venv venv
venv\Scripts\activate  # Windows
source venv/bin/activate  # Linux/Mac
```

3. 安装依赖包

```
pip install -r requirements.txt
```

4. 运行应用

```
python run.py
```

5. 在浏览器中访问 http://127.0.0.1:5000/ 即可使用应用

## 使用说明

### 添加交易记录

1. 点击"添加交易记录"按钮
2. 填写交易日期、时间、股票名称、代码、交易类型、价格和数量
3. 可选填写备注信息
4. 点击"提交"按钮保存记录

### 添加每日总结

1. 点击"添加每日总结"按钮
2. 选择日期
3. 填写当日盈亏金额（正数表示盈利，负数表示亏损）
4. 填写总结内容
5. 点击"提交"按钮保存总结

### 查看记录

- 点击"查看交易记录"可以查看所有交易记录
- 点击"查看每日总结"可以查看所有每日总结
- 点击记录中的"详情"按钮可以查看单条记录的详细信息

### 编辑记录

- 在交易记录列表或详情页面，点击"编辑"按钮可以修改交易记录
- 在每日总结列表或详情页面，点击"编辑"按钮可以修改每日总结
- 编辑页面会预填充原有数据，修改后点击"更新记录"或"更新总结"保存

### 删除记录

- 在交易记录列表或详情页面，点击"删除"按钮可以删除交易记录
- 在每日总结列表或详情页面，点击"删除"按钮可以删除每日总结
- 删除前会弹出确认对话框，确认后记录将被永久删除

### 自动获取股票名称

- 在添加或编辑交易记录时，输入股票代码后系统会自动获取对应的股票名称
- 支持纯数字代码（如600000）和带前缀的代码（如sh600000）
- 如果自动获取失败，可以手动输入股票名称
