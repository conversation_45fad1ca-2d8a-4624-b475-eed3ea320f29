{% extends "base.html" %}

{% block content %}
<div class="row">
    <div class="col-md-8 offset-md-2">
        <div class="card">
            <div class="card-header bg-warning text-dark">
                <h2 class="card-title">编辑交易记录</h2>
            </div>
            <div class="card-body">
                <form method="post" novalidate>
                    {{ form.hidden_tag() }}
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.date.label(class="form-label") }}
                                {{ form.date(class="form-control", type="date") }}
                                {% for error in form.date.errors %}
                                <span class="text-danger">{{ error }}</span>
                                {% endfor %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.time.label(class="form-label") }}
                                {{ form.time(class="form-control", type="time") }}
                                {% for error in form.time.errors %}
                                <span class="text-danger">{{ error }}</span>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.stock_name.label(class="form-label") }}
                                {{ form.stock_name(class="form-control") }}
                                {% for error in form.stock_name.errors %}
                                <span class="text-danger">{{ error }}</span>
                                {% endfor %}
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.stock_code.label(class="form-label") }}
                                <div class="input-group">
                                    {{ form.stock_code(class="form-control") }}
                                    <button type="button" id="test-stock-code" class="btn btn-outline-secondary">测试获取</button>
                                </div>
                                <small class="form-text text-muted">输入股票代码后自动获取名称，或点击"测试获取"按钮</small>
                                {% for error in form.stock_code.errors %}
                                <span class="text-danger">{{ error }}</span>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <div class="form-group">
                                {{ form.trade_type.label(class="form-label") }}
                                {{ form.trade_type(class="form-select") }}
                                {% for error in form.trade_type.errors %}
                                <span class="text-danger">{{ error }}</span>
                                {% endfor %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                {{ form.price.label(class="form-label") }}
                                {{ form.price(class="form-control") }}
                                {% for error in form.price.errors %}
                                <span class="text-danger">{{ error }}</span>
                                {% endfor %}
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="form-group">
                                {{ form.quantity.label(class="form-label") }}
                                {{ form.quantity(class="form-control") }}
                                {% for error in form.quantity.errors %}
                                <span class="text-danger">{{ error }}</span>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group mb-3">
                        {{ form.notes.label(class="form-label") }}
                        {{ form.notes(class="form-control", rows=3) }}
                        {% for error in form.notes.errors %}
                        <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                    </div>
                    
                    <div class="form-group">
                        {{ form.submit(class="btn btn-warning", value="更新记录") }}
                        <a href="{{ url_for('trade_detail', id=trade.id) }}" class="btn btn-secondary">取消</a>
                        <a href="{{ url_for('view_trades') }}" class="btn btn-outline-primary">返回列表</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
