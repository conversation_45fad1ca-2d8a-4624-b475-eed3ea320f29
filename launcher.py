import os
import sys
import time
import subprocess
import webbrowser
import signal
import tkinter as tk
from tkinter import messagebox, ttk
import threading

class StockTradingLauncher:
    def __init__(self, root):
        self.root = root
        self.root.title("股票交易记录系统启动器")
        self.root.geometry("400x300")
        self.root.resizable(False, False)
        
        # 设置图标（如果有的话）
        # self.root.iconbitmap("icon.ico")
        
        # 设置样式
        self.style = ttk.Style()
        self.style.configure("TButton", font=("微软雅黑", 10))
        self.style.configure("TLabel", font=("微软雅黑", 10))
        
        # 创建界面元素
        self.create_widgets()
        
        # 进程变量
        self.process = None
        self.is_running = False
        
        # 关闭窗口时的处理
        self.root.protocol("WM_DELETE_WINDOW", self.on_closing)
    
    def create_widgets(self):
        # 标题标签
        title_label = ttk.Label(self.root, text="股票交易记录系统", font=("微软雅黑", 16, "bold"))
        title_label.pack(pady=20)
        
        # 状态框
        self.status_frame = ttk.LabelFrame(self.root, text="系统状态")
        self.status_frame.pack(padx=20, pady=10, fill="x")
        
        self.status_label = ttk.Label(self.status_frame, text="未运行", foreground="red")
        self.status_label.pack(padx=10, pady=10)
        
        # 按钮框
        button_frame = ttk.Frame(self.root)
        button_frame.pack(padx=20, pady=20, fill="x")
        
        self.start_button = ttk.Button(button_frame, text="启动系统", command=self.start_server)
        self.start_button.pack(side="left", padx=10, expand=True, fill="x")
        
        self.open_browser_button = ttk.Button(button_frame, text="打开浏览器", command=self.open_browser, state="disabled")
        self.open_browser_button.pack(side="left", padx=10, expand=True, fill="x")
        
        self.stop_button = ttk.Button(button_frame, text="停止系统", command=self.stop_server, state="disabled")
        self.stop_button.pack(side="left", padx=10, expand=True, fill="x")
        
        # 进度条
        self.progress = ttk.Progressbar(self.root, mode="indeterminate")
        self.progress.pack(padx=20, pady=10, fill="x")
        
        # 版本信息
        version_label = ttk.Label(self.root, text="版本 1.0.0", font=("微软雅黑", 8))
        version_label.pack(side="bottom", pady=5)
    
    def start_server(self):
        if self.is_running:
            messagebox.showinfo("提示", "系统已经在运行中")
            return
        
        self.progress.start()
        self.start_button.config(state="disabled")
        
        # 在新线程中启动服务器
        threading.Thread(target=self._start_server_thread, daemon=True).start()
    
    def _start_server_thread(self):
        try:
            # 启动Flask应用
            self.process = subprocess.Popen([sys.executable, "run.py"], 
                                           stdout=subprocess.PIPE,
                                           stderr=subprocess.PIPE,
                                           creationflags=subprocess.CREATE_NO_WINDOW)
            
            # 等待服务器启动
            time.sleep(2)
            
            # 更新UI
            self.root.after(0, self._update_ui_after_start)
            
        except Exception as e:
            self.root.after(0, lambda: self._show_error(f"启动失败: {str(e)}"))
    
    def _update_ui_after_start(self):
        self.is_running = True
        self.progress.stop()
        self.status_label.config(text="正在运行", foreground="green")
        self.start_button.config(state="disabled")
        self.open_browser_button.config(state="normal")
        self.stop_button.config(state="normal")
        
        # 自动打开浏览器
        self.open_browser()
    
    def _show_error(self, message):
        self.progress.stop()
        self.start_button.config(state="normal")
        messagebox.showerror("错误", message)
    
    def open_browser(self):
        try:
            webbrowser.open("http://127.0.0.1:5000")
        except Exception as e:
            messagebox.showerror("错误", f"无法打开浏览器: {str(e)}")
    
    def stop_server(self):
        if not self.is_running:
            return
        
        try:
            # 在Windows上终止进程
            if self.process:
                self.process.terminate()
                # 等待进程结束
                self.process.wait(timeout=5)
            
            self.is_running = False
            self.status_label.config(text="未运行", foreground="red")
            self.start_button.config(state="normal")
            self.open_browser_button.config(state="disabled")
            self.stop_button.config(state="disabled")
            
        except Exception as e:
            messagebox.showerror("错误", f"停止服务器失败: {str(e)}")
    
    def on_closing(self):
        if self.is_running:
            if messagebox.askyesno("确认", "系统正在运行，确定要退出吗？"):
                self.stop_server()
                self.root.destroy()
        else:
            self.root.destroy()

if __name__ == "__main__":
    root = tk.Tk()
    app = StockTradingLauncher(root)
    root.mainloop()
