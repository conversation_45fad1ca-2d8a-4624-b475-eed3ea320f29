{% extends "base.html" %}

{% block content %}
<div class="row">
    <div class="col-md-8 offset-md-2">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h2 class="card-title">添加每日总结</h2>
            </div>
            <div class="card-body">
                <form method="post" novalidate>
                    {{ form.hidden_tag() }}
                    
                    <div class="form-group mb-3">
                        {{ form.date.label(class="form-label") }}
                        {{ form.date(class="form-control", type="date") }}
                        {% for error in form.date.errors %}
                        <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                    </div>
                    
                    <div class="form-group mb-3">
                        {{ form.profit_loss.label(class="form-label") }}
                        {{ form.profit_loss(class="form-control") }}
                        <small class="form-text text-muted">正数表示盈利，负数表示亏损</small>
                        {% for error in form.profit_loss.errors %}
                        <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                    </div>
                    
                    <div class="form-group mb-3">
                        {{ form.summary.label(class="form-label") }}
                        {{ form.summary(class="form-control", rows=5) }}
                        {% for error in form.summary.errors %}
                        <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                    </div>
                    
                    <div class="form-group">
                        {{ form.submit(class="btn btn-primary") }}
                        <a href="{{ url_for('index') }}" class="btn btn-secondary">取消</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}
