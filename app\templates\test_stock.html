{% extends "base.html" %}

{% block content %}
<div class="row">
    <div class="col-md-8 offset-md-2">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h2 class="card-title">测试股票代码查询</h2>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <label for="stock-code" class="form-label">股票代码</label>
                    <div class="input-group">
                        <input type="text" id="stock-code" class="form-control" placeholder="输入股票代码，如：600000">
                        <button id="query-btn" class="btn btn-primary">查询</button>
                    </div>
                    <small class="form-text text-muted">可以输入纯数字代码（如600000）或带前缀的代码（如sh600000）</small>
                </div>
                
                <div class="mb-3">
                    <label for="stock-name" class="form-label">股票名称</label>
                    <input type="text" id="stock-name" class="form-control" readonly>
                </div>
                
                <div class="mb-3">
                    <label for="api-response" class="form-label">API响应</label>
                    <textarea id="api-response" class="form-control" rows="10" readonly></textarea>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const stockCodeInput = document.getElementById('stock-code');
    const stockNameInput = document.getElementById('stock-name');
    const apiResponseTextarea = document.getElementById('api-response');
    const queryBtn = document.getElementById('query-btn');
    
    queryBtn.addEventListener('click', function() {
        const stockCode = stockCodeInput.value.trim();
        if (!stockCode) {
            alert('请输入股票代码');
            return;
        }
        
        // 显示加载提示
        stockNameInput.value = "正在获取...";
        apiResponseTextarea.value = "正在请求API...";
        
        // 处理股票代码格式
        let formattedCode = stockCode;
        
        // 如果已经包含前缀，直接使用
        if (stockCode.startsWith('sh') || stockCode.startsWith('sz') || stockCode.startsWith('bj')) {
            formattedCode = stockCode;
        }
        // 如果只输入数字，根据规则添加前缀
        else if (/^\d+$/.test(stockCode)) {
            // 确保股票代码至少有6位数字
            let paddedCode = stockCode.padStart(6, '0');
            
            if (paddedCode.startsWith('6')) {
                formattedCode = 'sh' + paddedCode;
            } else if (paddedCode.startsWith('0') || paddedCode.startsWith('3')) {
                formattedCode = 'sz' + paddedCode;
            } else if (paddedCode.startsWith('4') || paddedCode.startsWith('8')) {
                formattedCode = 'bj' + paddedCode;
            } else if (paddedCode.startsWith('5') || paddedCode.startsWith('1')) {
                formattedCode = 'sh' + paddedCode;
            }
        }
        
        console.log("原始代码:", stockCode, "格式化后:", formattedCode);
        
        // 调用后端API获取股票名称
        fetch('/get_stock_name?code=' + encodeURIComponent(formattedCode))
            .then(response => {
                console.log("API响应状态:", response.status);
                return response.json();
            })
            .then(data => {
                console.log("API返回数据:", data);
                apiResponseTextarea.value = JSON.stringify(data, null, 2);
                
                if (data.success && data.name) {
                    stockNameInput.value = data.name;
                } else {
                    stockNameInput.value = "";
                    alert('未找到股票信息，请检查股票代码是否正确');
                }
            })
            .catch(error => {
                console.error('获取股票名称失败:', error);
                stockNameInput.value = "";
                apiResponseTextarea.value = "错误: " + error.message;
                alert('获取股票名称失败，请检查网络连接');
            });
    });
});
</script>
{% endblock %}
