{% extends "base.html" %}

{% block content %}
<div class="row">
    <div class="col-md-10 offset-md-1">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h2 class="card-title">每日总结详情 - {{ summary.date.strftime('%Y-%m-%d') }}</h2>
            </div>
            <div class="card-body">
                <div class="row mb-4">
                    <div class="col-md-6">
                        <p><strong>日期：</strong> {{ summary.date.strftime('%Y-%m-%d') }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>盈亏金额：</strong>
                            {% if summary.profit_loss is not none %}
                                {% if summary.profit_loss > 0 %}
                                <span class="text-success">+{{ summary.profit_loss }}</span>
                                {% elif summary.profit_loss < 0 %}
                                <span class="text-danger">{{ summary.profit_loss }}</span>
                                {% else %}
                                <span>{{ summary.profit_loss }}</span>
                                {% endif %}
                            {% else %}
                            <span class="text-muted">未设置</span>
                            {% endif %}
                        </p>
                    </div>
                </div>

                <div class="row mb-4">
                    <div class="col-md-12">
                        <h4>总结内容</h4>
                        <div class="card">
                            <div class="card-body">
                                {{ summary.summary|replace('\n', '<br>')|safe }}
                            </div>
                        </div>
                    </div>
                </div>

                {% if trades %}
                <div class="row mb-4">
                    <div class="col-md-12">
                        <h4>当日交易记录</h4>
                        <div class="table-responsive">
                            <table class="table table-striped table-hover">
                                <thead>
                                    <tr>
                                        <th>时间</th>
                                        <th>股票名称</th>
                                        <th>股票代码</th>
                                        <th>交易类型</th>
                                        <th>价格</th>
                                        <th>数量</th>
                                        <th>总金额</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for trade in trades %}
                                    <tr>
                                        <td>{{ trade.time.strftime('%H:%M') }}</td>
                                        <td>{{ trade.stock_name }}</td>
                                        <td>{{ trade.stock_code }}</td>
                                        <td>
                                            {% if trade.trade_type == '买入' %}
                                            <span class="badge bg-success">买入</span>
                                            {% else %}
                                            <span class="badge bg-danger">卖出</span>
                                            {% endif %}
                                        </td>
                                        <td>{{ trade.price }}</td>
                                        <td>{{ trade.quantity }}</td>
                                        <td>{{ trade.total_amount }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                {% else %}
                <div class="row mb-4">
                    <div class="col-md-12">
                        <div class="alert alert-info">
                            该日期没有交易记录。
                        </div>
                    </div>
                </div>
                {% endif %}

                <div class="row">
                    <div class="col-md-12">
                        <a href="{{ url_for('view_summaries') }}" class="btn btn-primary">返回总结列表</a>
                        <a href="{{ url_for('edit_summary', id=summary.id) }}" class="btn btn-warning">编辑总结</a>
                        <button type="button" class="btn btn-danger" onclick="deleteSummaryRecord({{ summary.id }})">删除总结</button>
                        <a href="{{ url_for('add_summary') }}" class="btn btn-success">添加新总结</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
