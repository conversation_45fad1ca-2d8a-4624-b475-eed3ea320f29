{% extends "base.html" %}

{% block content %}
<div class="row">
    <div class="col-md-8 offset-md-2">
        <div class="card">
            <div class="card-header bg-success text-white">
                <h2 class="card-title">导入交易记录</h2>
            </div>
            <div class="card-body">
                <div class="alert alert-info">
                    <h5>导入说明：</h5>
                    <ul>
                        <li>支持Excel文件格式（.xlsx, .xls）</li>
                        <li>Excel文件必须包含以下列：<strong>成交时间、证券代码、证券名称、买卖方向、成交数量、成交价格</strong></li>
                        <li>成交时间格式：HH:MM:SS 或完整的日期时间</li>
                        <li>买卖方向：包含"买"或"卖"的文字</li>
                        <li>数量和价格必须为数字格式</li>
                        <li>最大文件大小：16MB</li>
                    </ul>
                </div>
                
                <form method="post" enctype="multipart/form-data" novalidate>
                    {{ form.hidden_tag() }}
                    
                    <div class="form-group mb-3">
                        {{ form.file.label(class="form-label") }}
                        {{ form.file(class="form-control", accept=".xlsx,.xls") }}
                        {% for error in form.file.errors %}
                        <span class="text-danger">{{ error }}</span>
                        {% endfor %}
                        <small class="form-text text-muted">请选择包含交易记录的Excel文件</small>
                    </div>
                    
                    <div class="form-group mb-3">
                        <h5>Excel文件格式示例：</h5>
                        <div class="table-responsive">
                            <table class="table table-bordered table-sm">
                                <thead class="table-light">
                                    <tr>
                                        <th>成交时间</th>
                                        <th>证券代码</th>
                                        <th>证券名称</th>
                                        <th>买卖方向</th>
                                        <th>成交数量</th>
                                        <th>成交价格</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>15:00:00.002136</td>
                                        <td>000001</td>
                                        <td>平安银行</td>
                                        <td>证券买入</td>
                                        <td>100</td>
                                        <td>7.5</td>
                                    </tr>
                                    <tr>
                                        <td>10:06:37.000510</td>
                                        <td>000510</td>
                                        <td>新金路</td>
                                        <td>证券卖出</td>
                                        <td>300</td>
                                        <td>5.89</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        {{ form.submit(class="btn btn-success") }}
                        <a href="{{ url_for('view_trades') }}" class="btn btn-secondary">取消</a>
                    </div>
                </form>
                
                <div class="mt-4">
                    <h5>注意事项：</h5>
                    <div class="alert alert-warning">
                        <ul class="mb-0">
                            <li>导入前请确保Excel文件格式正确</li>
                            <li>重复的交易记录也会被导入，请注意去重</li>
                            <li>导入过程中如有错误，系统会显示详细的错误信息</li>
                            <li>建议先备份现有数据再进行导入操作</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
