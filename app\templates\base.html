<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }} - 股市交易记录系统</title>
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css">
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="{{ url_for('index') }}">股市交易记录系统</a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('index') }}">首页</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('add_trade') }}">添加交易</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('import_trades') }}">导入交易</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('view_trades') }}">查看交易</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('add_summary') }}">添加总结</a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{{ url_for('view_summaries') }}">查看总结</a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        {% with messages = get_flashed_messages() %}
        {% if messages %}
        <div class="row">
            <div class="col-md-12">
                {% for message in messages %}
                <div class="alert alert-success">
                    {{ message }}
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
        {% endwith %}

        {% block content %}{% endblock %}
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/script.js') }}"></script>
</body>
</html>
