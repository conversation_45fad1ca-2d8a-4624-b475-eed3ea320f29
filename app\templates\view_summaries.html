{% extends "base.html" %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h2 class="card-title">每日总结列表</h2>
            </div>
            <div class="card-body">
                {% if summaries.items %}
                <div class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>日期</th>
                                <th>盈亏金额</th>
                                <th>总结摘要</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for summary in summaries.items %}
                            <tr>
                                <td>{{ summary.date.strftime('%Y-%m-%d') }}</td>
                                <td>
                                    {% if summary.profit_loss > 0 %}
                                    <span class="text-success">+{{ summary.profit_loss }}</span>
                                    {% elif summary.profit_loss < 0 %}
                                    <span class="text-danger">{{ summary.profit_loss }}</span>
                                    {% else %}
                                    <span>{{ summary.profit_loss }}</span>
                                    {% endif %}
                                </td>
                                <td>{{ summary.summary[:50] + '...' if summary.summary|length > 50 else summary.summary }}</td>
                                <td>
                                    <a href="{{ url_for('summary_detail', id=summary.id) }}" class="btn btn-sm btn-info">详情</a>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                
                <nav aria-label="Page navigation">
                    <ul class="pagination justify-content-center">
                        {% if summaries.has_prev %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('view_summaries', page=summaries.prev_num) }}">上一页</a>
                        </li>
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">上一页</span>
                        </li>
                        {% endif %}
                        
                        {% for page_num in summaries.iter_pages(left_edge=1, right_edge=1, left_current=1, right_current=2) %}
                            {% if page_num %}
                                {% if page_num == summaries.page %}
                                <li class="page-item active">
                                    <span class="page-link">{{ page_num }}</span>
                                </li>
                                {% else %}
                                <li class="page-item">
                                    <a class="page-link" href="{{ url_for('view_summaries', page=page_num) }}">{{ page_num }}</a>
                                </li>
                                {% endif %}
                            {% else %}
                                <li class="page-item disabled">
                                    <span class="page-link">...</span>
                                </li>
                            {% endif %}
                        {% endfor %}
                        
                        {% if summaries.has_next %}
                        <li class="page-item">
                            <a class="page-link" href="{{ url_for('view_summaries', page=summaries.next_num) }}">下一页</a>
                        </li>
                        {% else %}
                        <li class="page-item disabled">
                            <span class="page-link">下一页</span>
                        </li>
                        {% endif %}
                    </ul>
                </nav>
                {% else %}
                <div class="alert alert-info">
                    暂无每日总结。<a href="{{ url_for('add_summary') }}">添加一条每日总结</a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
