/* 自定义样式 */
body {
    background-color: #f5f5f5;
}

.navbar {
    margin-bottom: 20px;
}

.card {
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    margin-bottom: 20px;
}

.card-header {
    font-weight: bold;
}

.table th {
    background-color: #f8f9fa;
}

/* 交易类型标签样式 */
.badge {
    font-size: 0.9em;
    padding: 5px 10px;
}

/* 分页样式 */
.pagination {
    margin-top: 20px;
}

/* 表单样式 */
.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* 按钮样式 */
.btn {
    border-radius: 4px;
    padding: 6px 15px;
}

/* 盈亏金额颜色 */
.text-profit {
    color: #28a745;
}

.text-loss {
    color: #dc3545;
}

/* 操作按钮样式 */
.btn-group-actions {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
}

.btn-group-actions .btn {
    margin-bottom: 5px;
}

/* 确认删除对话框样式 */
.delete-confirm {
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
    padding: 10px;
    border-radius: 4px;
    margin: 10px 0;
}

/* 编辑表单特殊样式 */
.edit-form .card-header {
    background-color: #ffc107 !important;
    color: #212529 !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .container {
        padding: 0 10px;
    }

    .card-body {
        padding: 15px;
    }

    .btn-group-actions {
        flex-direction: column;
    }

    .btn-group-actions .btn {
        width: 100%;
        margin-bottom: 5px;
    }
}
