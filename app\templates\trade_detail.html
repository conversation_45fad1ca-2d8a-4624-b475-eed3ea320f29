{% extends "base.html" %}

{% block content %}
<div class="row">
    <div class="col-md-8 offset-md-2">
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h2 class="card-title">交易详情</h2>
            </div>
            <div class="card-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <p><strong>日期：</strong> {{ trade.date.strftime('%Y-%m-%d') }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>时间：</strong> {{ trade.time.strftime('%H:%M') }}</p>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-6">
                        <p><strong>股票名称：</strong> {{ trade.stock_name }}</p>
                    </div>
                    <div class="col-md-6">
                        <p><strong>股票代码：</strong> {{ trade.stock_code }}</p>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-4">
                        <p><strong>交易类型：</strong>
                            {% if trade.trade_type == '买入' %}
                            <span class="badge bg-success">买入</span>
                            {% else %}
                            <span class="badge bg-danger">卖出</span>
                            {% endif %}
                        </p>
                    </div>
                    <div class="col-md-4">
                        <p><strong>价格：</strong> {{ trade.price }}</p>
                    </div>
                    <div class="col-md-4">
                        <p><strong>数量：</strong> {{ trade.quantity }}</p>
                    </div>
                </div>

                <div class="row mb-3">
                    <div class="col-md-12">
                        <p><strong>总金额：</strong> {{ trade.total_amount }}</p>
                    </div>
                </div>

                {% if trade.notes %}
                <div class="row mb-3">
                    <div class="col-md-12">
                        <p><strong>备注：</strong></p>
                        <div class="card">
                            <div class="card-body">
                                {{ trade.notes }}
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}

                <div class="row">
                    <div class="col-md-12">
                        <a href="{{ url_for('view_trades') }}" class="btn btn-primary">返回交易列表</a>
                        <a href="{{ url_for('edit_trade', id=trade.id) }}" class="btn btn-warning">编辑记录</a>
                        <button type="button" class="btn btn-danger" onclick="deleteTradeRecord({{ trade.id }})">删除记录</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
