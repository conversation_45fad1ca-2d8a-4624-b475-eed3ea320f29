from flask_wtf import FlaskForm
from wtforms import <PERSON>Field, FloatField, IntegerField, TextAreaField, SelectField, DateField, TimeField, SubmitField
from wtforms.validators import DataRequired, Optional

class TradeForm(FlaskForm):
    date = DateField('交易日期', validators=[DataRequired()])
    time = TimeField('交易时间', validators=[DataRequired()])
    stock_name = StringField('股票名称', validators=[DataRequired()])
    stock_code = StringField('股票代码', validators=[DataRequired()])
    trade_type = SelectField('交易类型', choices=[('买入', '买入'), ('卖出', '卖出')], validators=[DataRequired()])
    price = FloatField('价格', validators=[DataRequired()])
    quantity = IntegerField('数量', validators=[DataRequired()])
    notes = TextAreaField('备注', validators=[Optional()])
    submit = SubmitField('提交')

class DailySummaryForm(FlaskForm):
    date = DateField('日期', validators=[DataRequired()])
    profit_loss = FloatField('盈亏金额', validators=[Optional()])
    summary = TextAreaField('每日总结', validators=[DataRequired()])
    submit = SubmitField('提交')
